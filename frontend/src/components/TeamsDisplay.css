/* TeamsDisplay.css */
.teams-container {
  margin-bottom: 20px;
  position: relative;
  z-index: 1000; /* Ensure teams stay above dimming overlay */
}

.teams-grid {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.team-card {
  flex: 1;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: white;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 300px; /* Ensure enough space for the paycheck */
}

.team-clickable:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}

/* Team 1 hover effect - only when clickable */
.team1-card.team-clickable {
  /* Add a transparent border by default to prevent layout shift on hover */
  border: 2px solid transparent;
}

.team1-card.team-clickable:hover {
  border-color: #2196F3;
  box-shadow: 0 6px 12px rgba(33, 150, 243, 0.3);
}

.team1-card.team-clickable:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(33, 203, 243, 0.1));
  z-index: 1;
  pointer-events: none;
}

/* Team 2 hover effect - only when clickable */
.team2-card.team-clickable {
  /* Add a transparent border by default to prevent layout shift on hover */
  border: 2px solid transparent;
}

.team2-card.team-clickable:hover {
  border-color: #F44336;
  box-shadow: 0 6px 12px rgba(244, 67, 54, 0.3);
}

.team2-card.team-clickable:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(244, 67, 54, 0.1), rgba(255, 152, 0, 0.1));
  z-index: 1;
  pointer-events: none;
}

/* Disabled team styling */
.team-disabled {
  opacity: 0.9;
}

.team-header {
  padding-left: 20px;
  padding-top: 10px;
  padding-bottom: 10px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.team-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 700;
  position: relative;
  z-index: 2;
}

/* Paycheck styling */
.paycheck {
  position: relative;
  margin-top: auto; /* Push to bottom */
  padding: 8px 10px;
  background: #f9f9f9;
  border-top: 1px solid #ddd;
  font-family: 'Courier New', monospace;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  font-size: 0.85rem;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.paycheck::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: repeating-linear-gradient(
    -45deg,
    transparent,
    transparent 3px,
    rgba(0, 0, 0, 0.05) 3px,
    rgba(0, 0, 0, 0.05) 6px
  );
}

.paycheck-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  padding-bottom: 3px;
  border-bottom: 1px dashed #ccc;
  font-size: 0.75rem;
  color: #555;
}

.paycheck-title {
  font-weight: bold;
  text-transform: uppercase;
}

.paycheck-date {
  font-style: italic;
}

.paycheck-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  font-weight: bold;
}

.paycheck-label {
  font-size: 0.7rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.paycheck-value {
  font-size: 1.1rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.paycheck-badge {
  display: inline-block;
  margin-left: 8px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: bold;
  color: white;
  vertical-align: middle;
}

.higher-score {
  background-color: #4CAF50;
}

.lower-score {
  background-color: #FF9800;
}

.equal-score {
  background-color: #9E9E9E;
}

.paycheck-signature {
  margin-top: 5px;
  padding-top: 3px;
  border-top: 1px dashed #ccc;
  font-style: italic;
  font-size: 0.7rem;
  color: #777;
  text-align: right;
}

/* Animation for paycheck */
@keyframes slideInFromBottom {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.paycheck {
  animation: slideInFromBottom 0.5s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
}

.team-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.9;
  z-index: 1;
}

.team1-header::before {
  background: linear-gradient(135deg, #2196F3, #21CBF3);
}

.team2-header::before {
  background: linear-gradient(135deg, #F44336, #FF9800);
}

.team-header h3 {
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.team-content {
  padding: 15px;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100% - 120px); /* Leave space for header and paycheck */
}

.team-score {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-weight: bold;
}

.team-score-value {
  margin-left: auto;
  font-size: 1.1rem;
  font-weight: 700;
}

.team-score-badge {
  display: inline-block;
  margin-left: 8px;
  padding: 3px 6px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: bold;
  color: white;
}

.higher-score {
  background-color: #4CAF50;
}

.lower-score {
  background-color: #FF9800;
}

.equal-score {
  background-color: #9E9E9E;
}

.player-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.player-item {
  padding: 10px 10px 10px 12px; /* Increased left padding */
  border-radius: 4px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  background-color: #f9f9f9;
  transition: transform 0.2s ease, background-color 0.2s ease;
  position: relative; /* For absolute positioning of leader indicator */
  overflow: hidden; /* Ensure long nicknames don't break layout */
}

.player-item:hover {
  transform: translateX(5px);
  background-color: #f0f0f0;
}

.player-name {
  flex-grow: 1;
  font-weight: normal;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px; /* Ensure some space between name and stats */
}

.player-leader {
  position: relative;
  border-left: 3px solid #ffd700 !important; /* Gold border on the left */
}

.player-leader::before {
  content: "★"; /* Star symbol */
  position: absolute;
  top: 50%;
  left: 4px;
  transform: translateY(-50%);
  font-size: 0.8rem;
  color: #ffd700;
  z-index: 1;
}

.player-leader .player-name {
  padding-left: 16px; /* Make space for the star */
}

.player-stats {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.player-wl {
  display: inline-block;
  font-size: 12px;
  margin-right: 8px;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 3px;
}

.player-score {
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 3px;
  min-width: 30px;
  text-align: center;
}

.empty-team {
  color: #666;
  font-style: italic;
  text-align: center;
  padding: 20px 0;
}

.balance-meter {
  margin-top: 10px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
  text-align: center;
}

.meter-label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.meter-bar {
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.meter-fill {
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.meter-perfect {
  background: linear-gradient(90deg, #4CAF50, #8BC34A);
}

.meter-good {
  background: linear-gradient(90deg, #8BC34A, #FFC107);
}

.meter-fair {
  background: linear-gradient(90deg, #FFC107, #FF9800);
}

.meter-poor {
  background: linear-gradient(90deg, #FF9800, #F44336);
}

.meter-value {
  margin-top: 8px;
  font-size: 0.9rem;
  color: #666;
}

/* Animation for team cards */
@keyframes slideInFromLeft {
  0% {
    transform: translateX(-50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(50px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.team1-card {
  animation: slideInFromLeft 0.5s ease-out forwards;
}

.team2-card {
  animation: slideInFromRight 0.5s ease-out forwards;
}

/* Animation for player items */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.player-item {
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

/* Staggered animation delay for player items */
.player-item:nth-child(1) { animation-delay: 0.1s; }
.player-item:nth-child(2) { animation-delay: 0.2s; }
.player-item:nth-child(3) { animation-delay: 0.3s; }
.player-item:nth-child(4) { animation-delay: 0.4s; }
.player-item:nth-child(5) { animation-delay: 0.5s; }
.player-item:nth-child(6) { animation-delay: 0.6s; }
.player-item:nth-child(7) { animation-delay: 0.7s; }
.player-item:nth-child(8) { animation-delay: 0.8s; }
.player-item:nth-child(9) { animation-delay: 0.9s; }
.player-item:nth-child(10) { animation-delay: 1.0s; }

/* Winning team styles */
.winning-team {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border-color: #4CAF50;
}

.winning-team .team-header::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.3), rgba(76, 175, 80, 0.1));
  z-index: 1;
  pointer-events: none;
}

.winning-badge {
  display: inline-block;
  margin-left: 10px;
  padding: 2px 8px;
  background-color: #4CAF50;
  color: white;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  vertical-align: middle;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* Submission status styles */
.submission-status {
  margin: 10px 0;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-weight: 500;
  transition: all 0.3s ease;
}

.submission-status.success {
  background-color: #E8F5E9;
  color: #2E7D32;
  border-left: 4px solid #4CAF50;
}

.submission-status.error {
  background-color: #FFEBEE;
  color: #C62828;
  border-left: 4px solid #F44336;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #673AB7;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Click indicator styles */
.click-indicator-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  text-align: center;
  pointer-events: none; /* Allow clicks to pass through */
  width: 100%;
}

.click-text {
  display: inline-block;
  padding: 6px 12px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.team-clickable:hover .click-text {
  opacity: 1;
  animation: fadeInOut 2s infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 1; }
}
